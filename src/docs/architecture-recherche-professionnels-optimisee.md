# Architecture Optimisée - Recherche de Professionnels

## 🎯 Objectifs de l'Optimisation

Cette refactorisation du système de recherche de professionnels vise à résoudre les problèmes de performance identifiés dans l'implémentation originale :

- **Récupération massive de données** : Limitation par pagination côté serveur
- **Filtrage côté client** : Déplacement du filtrage vers le serveur
- **Calcul systématique des disponibilités** : Chargement paresseux conditionnel
- **Absence de cache** : Implémentation d'un système de cache LRU avec TTL
- **UX dégradée** : Ajout de debouncing et d'indicateurs de chargement

## 🏗️ Architecture Générale

```
┌─────────────────────────────────────────────────────────────┐
│                    COUCHE PRÉSENTATION                      │
├─────────────────────────────────────────────────────────────┤
│ • useOptimizedProfessionalSearch (Hook principal)          │
│ • useDebounce (Gestion du debouncing)                      │
│ • SearchLoadingIndicator (Indicateurs visuels)             │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                     COUCHE DOMAINE                          │
├─────────────────────────────────────────────────────────────┤
│ • SearchProfessionalsOptimizedUsecase                      │
│ • SearchCache (Service de cache)                           │
│ • ProfessionalAvailabilitiesFilter                         │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                 COUCHE INFRASTRUCTURE                       │
├─────────────────────────────────────────────────────────────┤
│ • SearchProfessionalsOptimizedRepository                   │
│ • Base de données avec requêtes optimisées                 │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Composants Principaux

### 1. SearchProfessionalsOptimizedUsecase

**Responsabilités :**
- Orchestration de la recherche avec cache
- Gestion de la pagination
- Chargement conditionnel des disponibilités
- Gestion des erreurs

**Optimisations :**
- Vérification du cache avant chaque requête
- Calcul des disponibilités uniquement si demandé
- Traitement asynchrone en parallèle

### 2. SearchCache

**Caractéristiques :**
- Cache LRU (Least Recently Used) en mémoire
- TTL configurable (défaut: 5 minutes)
- Nettoyage automatique des entrées expirées
- Génération de clés basée sur le hash des paramètres

**Avantages :**
- Réduction drastique des requêtes répétitives
- Amélioration de la réactivité de l'interface
- Gestion intelligente de la mémoire

### 3. useOptimizedProfessionalSearch

**Fonctionnalités :**
- Debouncing automatique des recherches (300ms par défaut)
- Gestion des états de chargement multiples
- Pagination avec chargement incrémental
- Chargement paresseux des disponibilités
- Annulation des requêtes en cours

## 📊 Améliorations de Performance

### Avant l'Optimisation

```typescript
// ❌ Problèmes identifiés
const data = await repository.execute(params); // Récupère TOUT
const filtered = data.filter(complexFilter);   // Filtre côté client
const withAvailabilities = filtered.map(prof => {
  return calculateAvailabilities(prof);        // Calcul pour TOUS
});
```

### Après l'Optimisation

```typescript
// ✅ Solutions implémentées
const cacheKey = cache.generateKey(params);
const cached = await cache.get(cacheKey);     // Vérification cache
if (cached) return cached;

const paginated = await repository.execute({  // Pagination serveur
  ...params,
  pagination: { page: 1, pageSize: 10 }
});

const withConditionalAvailabilities = await Promise.all(
  paginated.data.map(async prof => {
    if (params.includeAvailabilities) {        // Conditionnel
      return await calculateAvailabilities(prof);
    }
    return prof;
  })
);
```

## 🎨 Améliorations UX

### Debouncing Intelligent

- **Délai configurable** : 300ms par défaut, ajustable selon le contexte
- **Annulation automatique** : Les requêtes en cours sont annulées
- **États visuels** : Indicateurs de recherche en cours

### Indicateurs de Chargement

- **Recherche initiale** : Icône de recherche avec animation
- **Chargement de pages** : Spinner pour "Charger plus"
- **Disponibilités** : Indicateur spécifique par professionnel

### Pagination Intelligente

- **Chargement incrémental** : Ajout des résultats sans remplacement
- **Détection automatique** : Bouton "Charger plus" selon disponibilité
- **Gestion d'erreurs** : Retry automatique en cas d'échec

## 🔄 Flux de Données Optimisé

```mermaid
sequenceDiagram
    participant U as Utilisateur
    participant H as Hook
    participant C as Cache
    participant UC as UseCase
    participant R as Repository

    U->>H: Saisie recherche
    H->>H: Debouncing (300ms)
    H->>UC: execute(params)
    UC->>C: get(cacheKey)
    alt Cache Hit
        C-->>UC: Données mises en cache
        UC-->>H: Résultat immédiat
    else Cache Miss
        UC->>R: execute(params)
        R-->>UC: Données paginées
        UC->>C: set(cacheKey, result)
        UC-->>H: Résultat frais
    end
    H-->>U: Affichage résultats
```

## 📈 Métriques de Performance Attendues

### Temps de Réponse

- **Cache Hit** : ~10ms (vs 500-2000ms avant)
- **Cache Miss** : ~200-500ms (vs 2000-5000ms avant)
- **Chargement page suivante** : ~100-300ms

### Utilisation Réseau

- **Réduction requêtes** : -70% grâce au cache
- **Taille des réponses** : -80% grâce à la pagination
- **Bande passante** : -60% globalement

### Expérience Utilisateur

- **Réactivité** : Recherche instantanée avec debouncing
- **Fluidité** : Pas de blocage interface pendant les calculs
- **Feedback** : Indicateurs visuels appropriés

## 🛠️ Configuration et Utilisation

### Configuration du Hook

```typescript
const {
  professionals,
  isSearching,
  updateSearchTerm,
  loadMore,
  canLoadMore
} = useOptimizedProfessionalSearch(searchUsecase, {
  debounceDelay: 300,        // Délai de debouncing
  defaultPageSize: 10,       // Taille de page
  includeAvailabilities: false // Chargement paresseux
});
```

### Configuration du Cache

```typescript
const cache = new SearchCache(
  100,  // Taille maximale (nombre d'entrées)
  300   // TTL en secondes
);
```

## 🔮 Évolutions Futures

### Optimisations Supplémentaires

1. **Cache persistant** : Utilisation de IndexedDB pour la persistance
2. **Préchargement intelligent** : Anticipation des recherches probables
3. **Compression** : Réduction de la taille des données mises en cache
4. **Métriques** : Collecte de données de performance en temps réel

### Fonctionnalités Avancées

1. **Recherche géolocalisée** : Tri par proximité
2. **Filtres dynamiques** : Mise à jour en temps réel des options
3. **Recommandations** : Suggestions basées sur l'historique
4. **Mode hors ligne** : Fonctionnement avec cache local

## 📝 Notes d'Implémentation

- **Compatibilité** : L'ancienne API reste fonctionnelle pendant la migration
- **Tests** : Tests unitaires et d'intégration à implémenter
- **Monitoring** : Logs de performance pour optimisations futures
- **Documentation** : JSDoc complet sur tous les composants
