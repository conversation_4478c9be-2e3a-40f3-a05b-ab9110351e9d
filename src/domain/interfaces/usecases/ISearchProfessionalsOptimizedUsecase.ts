import { ProfessionalCardDTO } from "@/domain/DTOS/ProfessionalDTO";
import { 
  SearchProfessionalsOptimizedParams, 
  PaginatedResult 
} from "@/domain/usecases/professional/GetProfessionnalInformations/types.optimized";

/**
 * Interface pour le usecase optimisé de recherche de professionnels
 * Supporte la pagination, le filtrage côté serveur et le chargement paresseux
 */
export interface ISearchProfessionalsOptimizedUsecase {
  /**
   * Recherche paginée de professionnels avec optimisations
   * @param searchParams - Paramètres de recherche avec pagination et filtres
   * @returns Résultat paginé avec métadonnées
   */
  execute(
    searchParams: SearchProfessionalsOptimizedParams
  ): Promise<PaginatedResult<ProfessionalCardDTO>>;

  /**
   * Charge les disponibilités pour un professionnel spécifique
   * Utilisé pour le chargement paresseux des disponibilités
   * @param professionalId - ID du professionnel
   * @param today - Date du jour
   * @returns Disponibilités formatées
   */
  loadAvailabilities(
    professionalId: number,
    today: string
  ): Promise<ProfessionalCardDTO['disponibilite']>;
}
