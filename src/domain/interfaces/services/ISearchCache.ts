import { ProfessionalCardDTO } from "@/domain/DTOS/ProfessionalDTO";
import { 
  SearchProfessionalsOptimizedParams, 
  PaginatedResult 
} from "@/domain/usecases/professional/GetProfessionnalInformations/types.optimized";

/**
 * Interface pour le service de cache des recherches de professionnels
 * Permet d'éviter les requêtes répétitives et d'améliorer les performances
 */
export interface ISearchCache {
  /**
   * Récupère un résultat de recherche depuis le cache
   * @param key - Clé de cache générée à partir des paramètres de recherche
   * @returns Résultat mis en cache ou null si non trouvé
   */
  get(key: string): Promise<PaginatedResult<ProfessionalCardDTO> | null>;

  /**
   * Stocke un résultat de recherche dans le cache
   * @param key - Clé de cache générée à partir des paramètres de recherche
   * @param result - Résultat à mettre en cache
   * @param ttl - Durée de vie en secondes (optionnel, défaut: 300s)
   */
  set(
    key: string, 
    result: PaginatedResult<ProfessionalCardDTO>, 
    ttl?: number
  ): Promise<void>;

  /**
   * Génère une clé de cache à partir des paramètres de recherche
   * @param params - Paramètres de recherche
   * @returns Clé de cache unique
   */
  generateKey(params: SearchProfessionalsOptimizedParams): string;

  /**
   * Invalide le cache pour une clé spécifique
   * @param key - Clé de cache à invalider
   */
  invalidate(key: string): Promise<void>;

  /**
   * Vide complètement le cache
   */
  clear(): Promise<void>;
}
