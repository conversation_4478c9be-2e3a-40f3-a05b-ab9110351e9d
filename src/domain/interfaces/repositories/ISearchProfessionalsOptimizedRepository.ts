import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";
import { 
  SearchProfessionalsOptimizedParams, 
  PaginatedResult 
} from "@/domain/usecases/professional/GetProfessionnalInformations/types.optimized";

/**
 * Interface pour le repository optimisé de recherche de professionnels
 * Supporte la pagination et le filtrage côté serveur
 */
export interface ISearchProfessionalsOptimizedRepository {
  /**
   * Recherche paginée de professionnels avec filtrage côté serveur
   * @param params - Paramètres de recherche avec pagination et filtres
   * @returns Résultat paginé avec métadonnées
   */
  execute(
    params: SearchProfessionalsOptimizedParams
  ): Promise<PaginatedResult<SearchProfessionalDTO>>;

  /**
   * Récupère les données d'un professionnel spécifique pour le chargement paresseux
   * @param professionalId - ID du professionnel
   * @returns Données complètes du professionnel
   */
  getProfessionalById(
    professionalId: number
  ): Promise<SearchProfessionalDTO>;
}
