import { ISearchCache } from "@/domain/interfaces/services/ISearchCache";
import { ProfessionalCardDTO } from "@/domain/DTOS/ProfessionalDTO";
import { 
  SearchProfessionalsOptimizedParams, 
  PaginatedResult 
} from "@/domain/usecases/professional/GetProfessionnalInformations/types.optimized";

/**
 * Entrée de cache avec métadonnées
 */
interface CacheEntry {
  /** Données mises en cache */
  data: PaginatedResult<ProfessionalCardDTO>;
  /** Timestamp d'expiration */
  expiresAt: number;
  /** Timestamp de création */
  createdAt: number;
}

/**
 * Service de cache en mémoire pour les recherches de professionnels
 * Implémente un système LRU (Least Recently Used) avec TTL
 */
export class SearchCache implements ISearchCache {
  private cache = new Map<string, CacheEntry>();
  private readonly maxSize: number;
  private readonly defaultTtl: number;

  constructor(maxSize: number = 100, defaultTtl: number = 300) {
    this.maxSize = maxSize;
    this.defaultTtl = defaultTtl; // 5 minutes par défaut
  }

  /**
   * Récupère un résultat depuis le cache
   */
  async get(key: string): Promise<PaginatedResult<ProfessionalCardDTO> | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Vérification de l'expiration
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    // Mise à jour de l'ordre LRU (suppression et réinsertion)
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.data;
  }

  /**
   * Stocke un résultat dans le cache
   */
  async set(
    key: string, 
    result: PaginatedResult<ProfessionalCardDTO>, 
    ttl?: number
  ): Promise<void> {
    const actualTtl = ttl || this.defaultTtl;
    const now = Date.now();
    
    const entry: CacheEntry = {
      data: result,
      expiresAt: now + (actualTtl * 1000),
      createdAt: now,
    };

    // Suppression des entrées expirées avant l'ajout
    this.cleanExpiredEntries();

    // Gestion de la taille maximale du cache (LRU)
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    this.cache.set(key, entry);
  }

  /**
   * Génère une clé de cache unique à partir des paramètres de recherche
   */
  generateKey(params: SearchProfessionalsOptimizedParams): string {
    // Normalisation des paramètres pour une clé cohérente
    const normalizedParams = {
      name: params.name.toLowerCase().trim(),
      localization: params.localization.toLowerCase().trim(),
      today: params.today,
      page: params.pagination.page,
      pageSize: params.pagination.pageSize,
      includeAvailabilities: params.includeAvailabilities || false,
      filters: params.filters ? {
        gender: params.filters.gender?.toLowerCase(),
        status: params.filters.status?.toLowerCase(),
        speciality: params.filters.speciality?.toLowerCase(),
        language: params.filters.language?.map(l => l.toLowerCase()).sort(),
      } : null,
    };

    // Génération d'une clé basée sur le hash des paramètres
    return this.hashObject(normalizedParams);
  }

  /**
   * Invalide une entrée spécifique du cache
   */
  async invalidate(key: string): Promise<void> {
    this.cache.delete(key);
  }

  /**
   * Vide complètement le cache
   */
  async clear(): Promise<void> {
    this.cache.clear();
  }

  /**
   * Nettoie les entrées expirées du cache
   */
  private cleanExpiredEntries(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  /**
   * Génère un hash simple pour un objet
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Conversion en 32bit
    }
    
    return `search_${Math.abs(hash).toString(36)}`;
  }

  /**
   * Retourne les statistiques du cache (pour le debugging)
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate?: number;
  } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
    };
  }
}
