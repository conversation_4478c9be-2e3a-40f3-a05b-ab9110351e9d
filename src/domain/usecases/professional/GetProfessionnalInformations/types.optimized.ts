/**
 * Types pour l'optimisation de la recherche de professionnels
 * Ces interfaces permettent d'implémenter la pagination, le filtrage côté serveur
 * et le chargement paresseux des disponibilités
 */

/**
 * Paramètres de pagination pour les requêtes
 */
export interface PaginationParams {
  /** Page actuelle (commence à 1) */
  page: number;
  /** Nombre d'éléments par page */
  pageSize: number;
}

/**
 * Paramètres de recherche optimisés avec pagination
 */
export interface SearchProfessionalsOptimizedParams {
  /** Terme de recherche (nom, prénom, spécialité, etc.) */
  name: string;
  /** Localisation (ville, code postal, etc.) */
  localization: string;
  /** Date du jour (format compatible avec dayjs) */
  today: string;
  /** Paramètres de pagination */
  pagination: PaginationParams;
  /** Indique si les disponibilités doivent être calculées */
  includeAvailabilities?: boolean;
  /** Filtres additionnels */
  filters?: ProfessionalFilters;
}

/**
 * Filtres pour la recherche de professionnels
 */
export interface ProfessionalFilters {
  /** Filtre par sexe */
  gender?: string;
  /** Filtre par statut d'acceptation de nouveaux patients */
  status?: string;
  /** Filtre par spécialité */
  speciality?: string;
  /** Filtre par langue parlée */
  language?: string[];
}

/**
 * Résultat paginé de la recherche de professionnels
 */
export interface PaginatedResult<T> {
  /** Données de la page courante */
  data: T[];
  /** Métadonnées de pagination */
  pagination: {
    /** Page actuelle */
    currentPage: number;
    /** Nombre total de pages */
    totalPages: number;
    /** Nombre total d'éléments */
    totalItems: number;
    /** Nombre d'éléments par page */
    pageSize: number;
    /** Indique s'il y a une page suivante */
    hasNextPage: boolean;
    /** Indique s'il y a une page précédente */
    hasPreviousPage: boolean;
  };
}
