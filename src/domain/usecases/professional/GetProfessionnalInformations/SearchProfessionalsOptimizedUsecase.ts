import { ISearchProfessionalsOptimizedRepository } from "@/domain/interfaces/repositories/ISearchProfessionalsOptimizedRepository";
import { ISearchProfessionalsOptimizedUsecase } from "@/domain/interfaces/usecases/ISearchProfessionalsOptimizedUsecase";
import { ISearchCache } from "@/domain/interfaces/services/ISearchCache";
import { IProfessionalAvailabilitiesFilter } from "@/domain/interfaces/services/IProfessionalAvaiabilitiesFilter";
import { 
  SearchProfessionalsOptimizedParams, 
  PaginatedResult 
} from "./types.optimized";
import { ProfessionalCardDTO, TimeSlotProffessionalCard } from "@/domain/DTOS";

/**
 * Usecase optimisé pour la recherche de professionnels
 * Implémente la pagination, le cache et le chargement paresseux des disponibilités
 */
export class SearchProfessionalsOptimizedUsecase implements ISearchProfessionalsOptimizedUsecase {
  constructor(
    private readonly repository: ISearchProfessionalsOptimizedRepository,
    private readonly cache: ISearchCache,
    private readonly availabilitiesFilter: IProfessionalAvailabilitiesFilter,
  ) {}

  /**
   * Recherche paginée de professionnels avec optimisations
   */
  async execute(
    searchParams: SearchProfessionalsOptimizedParams
  ): Promise<PaginatedResult<ProfessionalCardDTO>> {
    try {
      // Génération de la clé de cache
      const cacheKey = this.cache.generateKey(searchParams);
      
      // Vérification du cache
      const cachedResult = await this.cache.get(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      // Recherche avec pagination côté serveur
      const paginatedData = await this.repository.execute(searchParams);

      // Transformation des données
      const formattedData: ProfessionalCardDTO[] = await Promise.all(
        paginatedData.data.map(async (professional) => {
          // Chargement conditionnel des disponibilités
          let disponibilite: TimeSlotProffessionalCard[] = [];
          
          if (searchParams.includeAvailabilities && 
              professional.parametre_disponibilite.length > 0) {
            disponibilite = await this.calculateAvailabilities(
              professional, 
              searchParams.today
            );
          }

          return {
            ...professional,
            disponibilite,
            specialite: professional.specialites_professionnel,
          };
        })
      );

      // Construction du résultat final
      const result: PaginatedResult<ProfessionalCardDTO> = {
        data: formattedData,
        pagination: paginatedData.pagination,
      };

      // Mise en cache du résultat (TTL: 5 minutes)
      await this.cache.set(cacheKey, result, 300);

      return result;
    } catch (error) {
      console.error("Erreur lors de la recherche de professionnels:", error);
      throw new Error("Impossible de récupérer les professionnels");
    }
  }

  /**
   * Charge les disponibilités pour un professionnel spécifique
   * Utilisé pour le chargement paresseux
   */
  async loadAvailabilities(
    professionalId: number,
    today: string
  ): Promise<TimeSlotProffessionalCard[]> {
    try {
      const professional = await this.repository.getProfessionalById(professionalId);
      
      if (!professional || professional.parametre_disponibilite.length === 0) {
        return [];
      }

      return this.calculateAvailabilities(professional, today);
    } catch (error) {
      console.error("Erreur lors du chargement des disponibilités:", error);
      return [];
    }
  }

  /**
   * Calcule les disponibilités d'un professionnel
   * Méthode privée pour éviter la duplication de code
   */
  private async calculateAvailabilities(
    professional: any,
    today: string
  ): Promise<TimeSlotProffessionalCard[]> {
    return this.availabilitiesFilter.filter(
      professional.parametre_disponibilite[0],
      professional.rendez_vous || [],
      professional.evenement || [],
      professional.parametre_disponibilite[0].temps_moyen_consulation || 30,
    );
  }
}
