import { useState, useEffect, useCallback, useRef } from 'react';
import { ProfessionalCardDTO } from '@/domain/DTOS/ProfessionalDTO';
import { 
  SearchProfessionalsOptimizedParams, 
  PaginatedResult,
  ProfessionalFilters 
} from '@/domain/usecases/professional/GetProfessionnalInformations/types.optimized';

/**
 * États de chargement pour différentes opérations
 */
interface LoadingStates {
  /** Chargement initial ou nouvelle recherche */
  searching: boolean;
  /** Chargement de la page suivante */
  loadingMore: boolean;
  /** Chargement des disponibilités d'un professionnel */
  loadingAvailabilities: Record<number, boolean>;
}

/**
 * Configuration du hook
 */
interface UseOptimizedProfessionalSearchConfig {
  /** Délai de debouncing en millisecondes */
  debounceDelay?: number;
  /** Taille de page par défaut */
  defaultPageSize?: number;
  /** Inclure les disponibilités par défaut */
  includeAvailabilities?: boolean;
}

/**
 * Hook optimisé pour la recherche de professionnels
 * Gère le debouncing, la pagination et les états de chargement
 */
export const useOptimizedProfessionalSearch = (
  searchUsecase: any, // ISearchProfessionalsOptimizedUsecase
  config: UseOptimizedProfessionalSearchConfig = {}
) => {
  const {
    debounceDelay = 300,
    defaultPageSize = 10,
    includeAvailabilities = false,
  } = config;

  // États principaux
  const [searchTerm, setSearchTerm] = useState('');
  const [localization, setLocalization] = useState('');
  const [filters, setFilters] = useState<ProfessionalFilters>({});
  const [professionals, setProfessionals] = useState<ProfessionalCardDTO[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: defaultPageSize,
    hasNextPage: false,
    hasPreviousPage: false,
  });

  // États de chargement
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({
    searching: false,
    loadingMore: false,
    loadingAvailabilities: {},
  });

  // État d'erreur
  const [error, setError] = useState<string | null>(null);

  // Références pour le debouncing
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();

  /**
   * Fonction de recherche principale
   */
  const performSearch = useCallback(async (
    searchParams: Partial<SearchProfessionalsOptimizedParams>,
    isLoadMore = false
  ) => {
    try {
      // Annulation de la requête précédente
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      // Mise à jour des états de chargement
      setLoadingStates(prev => ({
        ...prev,
        searching: !isLoadMore,
        loadingMore: isLoadMore,
      }));

      setError(null);

      // Construction des paramètres complets
      const params: SearchProfessionalsOptimizedParams = {
        name: searchParams.name || searchTerm,
        localization: searchParams.localization || localization,
        today: new Date().toISOString(),
        pagination: {
          page: searchParams.pagination?.page || 1,
          pageSize: defaultPageSize,
        },
        includeAvailabilities,
        filters,
        ...searchParams,
      };

      // Exécution de la recherche
      const result: PaginatedResult<ProfessionalCardDTO> = await searchUsecase.execute(params);

      // Mise à jour des résultats
      if (isLoadMore) {
        setProfessionals(prev => [...prev, ...result.data]);
      } else {
        setProfessionals(result.data);
      }

      setPagination(result.pagination);

    } catch (err: any) {
      if (err.name !== 'AbortError') {
        setError('Erreur lors de la recherche de professionnels');
        console.error('Erreur de recherche:', err);
      }
    } finally {
      setLoadingStates(prev => ({
        ...prev,
        searching: false,
        loadingMore: false,
      }));
    }
  }, [searchTerm, localization, filters, includeAvailabilities, defaultPageSize, searchUsecase]);

  /**
   * Recherche avec debouncing
   */
  const debouncedSearch = useCallback((
    searchParams: Partial<SearchProfessionalsOptimizedParams> = {}
  ) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      performSearch({ ...searchParams, pagination: { page: 1, pageSize: defaultPageSize } });
    }, debounceDelay);
  }, [performSearch, debounceDelay, defaultPageSize]);

  /**
   * Chargement de la page suivante
   */
  const loadMore = useCallback(() => {
    if (pagination.hasNextPage && !loadingStates.loadingMore) {
      performSearch({
        pagination: {
          page: pagination.currentPage + 1,
          pageSize: defaultPageSize,
        }
      }, true);
    }
  }, [pagination, loadingStates.loadingMore, performSearch, defaultPageSize]);

  /**
   * Chargement paresseux des disponibilités
   */
  const loadAvailabilities = useCallback(async (professionalId: number) => {
    try {
      setLoadingStates(prev => ({
        ...prev,
        loadingAvailabilities: {
          ...prev.loadingAvailabilities,
          [professionalId]: true,
        },
      }));

      const availabilities = await searchUsecase.loadAvailabilities(
        professionalId,
        new Date().toISOString()
      );

      // Mise à jour du professionnel avec ses disponibilités
      setProfessionals(prev => prev.map(prof => 
        prof.id === professionalId 
          ? { ...prof, disponibilite: availabilities }
          : prof
      ));

    } catch (err) {
      console.error('Erreur lors du chargement des disponibilités:', err);
    } finally {
      setLoadingStates(prev => ({
        ...prev,
        loadingAvailabilities: {
          ...prev.loadingAvailabilities,
          [professionalId]: false,
        },
      }));
    }
  }, [searchUsecase]);

  /**
   * Mise à jour du terme de recherche avec debouncing
   */
  const updateSearchTerm = useCallback((term: string) => {
    setSearchTerm(term);
    debouncedSearch({ name: term });
  }, [debouncedSearch]);

  /**
   * Mise à jour de la localisation avec debouncing
   */
  const updateLocalization = useCallback((location: string) => {
    setLocalization(location);
    debouncedSearch({ localization: location });
  }, [debouncedSearch]);

  /**
   * Mise à jour des filtres avec debouncing
   */
  const updateFilters = useCallback((newFilters: Partial<ProfessionalFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    debouncedSearch({ filters: updatedFilters });
  }, [filters, debouncedSearch]);

  /**
   * Réinitialisation de la recherche
   */
  const resetSearch = useCallback(() => {
    setSearchTerm('');
    setLocalization('');
    setFilters({});
    setProfessionals([]);
    setPagination({
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,
      pageSize: defaultPageSize,
      hasNextPage: false,
      hasPreviousPage: false,
    });
    setError(null);
  }, [defaultPageSize]);

  // Nettoyage lors du démontage
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    // États
    searchTerm,
    localization,
    filters,
    professionals,
    pagination,
    loadingStates,
    error,

    // Actions
    updateSearchTerm,
    updateLocalization,
    updateFilters,
    loadMore,
    loadAvailabilities,
    resetSearch,
    performSearch: debouncedSearch,

    // États dérivés
    isSearching: loadingStates.searching,
    isLoadingMore: loadingStates.loadingMore,
    hasResults: professionals.length > 0,
    canLoadMore: pagination.hasNextPage && !loadingStates.loadingMore,
  };
};
