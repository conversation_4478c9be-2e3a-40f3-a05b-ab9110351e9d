import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Hook pour gérer le debouncing d'une valeur
 * @param value - Valeur à debouncer
 * @param delay - Délai en millisecondes
 * @returns Valeur debouncée
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook pour gérer le debouncing d'une fonction
 * @param callback - Fonction à debouncer
 * @param delay - Délai en millisecondes
 * @returns Fonction debouncée
 */
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  // Nettoyage lors du démontage
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
};

/**
 * Hook avancé pour le debouncing avec contrôle d'annulation
 * @param callback - Fonction à debouncer
 * @param delay - Délai en millisecondes
 * @returns Objet avec la fonction debouncée et les contrôles
 */
export const useAdvancedDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const [isPending, setIsPending] = useState(false);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      setIsPending(true);

      timeoutRef.current = setTimeout(() => {
        setIsPending(false);
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      setIsPending(false);
    }
  }, []);

  const flush = useCallback(
    (...args: Parameters<T>) => {
      cancel();
      setIsPending(false);
      callback(...args);
    },
    [callback, cancel]
  ) as T;

  // Nettoyage lors du démontage
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    /** Fonction debouncée */
    debouncedCallback,
    /** Indique si un appel est en attente */
    isPending,
    /** Annule l'appel en attente */
    cancel,
    /** Exécute immédiatement la fonction en annulant le debounce */
    flush,
  };
};
