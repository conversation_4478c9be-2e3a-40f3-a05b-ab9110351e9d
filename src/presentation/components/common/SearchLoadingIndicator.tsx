import React from 'react';
import { Search, Loader2, Users } from 'lucide-react';

/**
 * Props pour le composant d'indicateur de chargement de recherche
 */
interface SearchLoadingIndicatorProps {
  /** Type de chargement en cours */
  type: 'searching' | 'loadingMore' | 'loadingAvailabilities';
  /** Texte personnalisé (optionnel) */
  text?: string;
  /** Taille de l'indicateur */
  size?: 'sm' | 'md' | 'lg';
  /** Affichage en mode compact */
  compact?: boolean;
}

/**
 * Composant d'indicateur de chargement pour les recherches de professionnels
 * Affiche différents états de chargement avec des animations appropriées
 */
const SearchLoadingIndicator: React.FC<SearchLoadingIndicatorProps> = ({
  type,
  text,
  size = 'md',
  compact = false,
}) => {
  // Configuration des tailles
  const sizeConfig = {
    sm: { icon: 16, text: 'text-sm', spacing: 'gap-2' },
    md: { icon: 20, text: 'text-base', spacing: 'gap-3' },
    lg: { icon: 24, text: 'text-lg', spacing: 'gap-4' },
  };

  const config = sizeConfig[size];

  // Configuration des messages par type
  const getContent = () => {
    switch (type) {
      case 'searching':
        return {
          icon: <Search size={config.icon} className="animate-pulse" />,
          defaultText: 'Recherche en cours...',
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-700',
          borderColor: 'border-blue-200',
        };
      case 'loadingMore':
        return {
          icon: <Loader2 size={config.icon} className="animate-spin" />,
          defaultText: 'Chargement des résultats suivants...',
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-700',
          borderColor: 'border-gray-200',
        };
      case 'loadingAvailabilities':
        return {
          icon: <Users size={config.icon} className="animate-pulse" />,
          defaultText: 'Chargement des disponibilités...',
          bgColor: 'bg-green-50',
          textColor: 'text-green-700',
          borderColor: 'border-green-200',
        };
      default:
        return {
          icon: <Loader2 size={config.icon} className="animate-spin" />,
          defaultText: 'Chargement...',
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-700',
          borderColor: 'border-gray-200',
        };
    }
  };

  const content = getContent();
  const displayText = text || content.defaultText;

  if (compact) {
    return (
      <div className={`inline-flex items-center ${config.spacing} ${content.textColor}`}>
        {content.icon}
        <span className={config.text}>{displayText}</span>
      </div>
    );
  }

  return (
    <div className={`
      flex items-center justify-center p-4 rounded-lg border
      ${content.bgColor} ${content.borderColor} ${config.spacing}
    `}>
      {content.icon}
      <span className={`${config.text} ${content.textColor} font-medium`}>
        {displayText}
      </span>
    </div>
  );
};

export default SearchLoadingIndicator;
